import { Textbox, Rect, Line, Circle, Canvas, Group } from "fabric";
import { ToolMode, FabricMeasurementLine } from "@/shared/types";
import { getToolConfig, transformPointer } from "./toolConfigs";
import { createArrow, updateArrowSize } from "@/lib/fabric/operations/arrows";
import { createMeasurementLine, updateMeasurementSize } from "@/lib/fabric/operations/measurements";
import { v4 as uuidv4 } from "uuid";

/*
Factory for tool-specific initial Fabric objects at the transformed pointer.
Uses getToolConfig(mode) for defaults and transformPointer for proper coords.
*/
export const createInitialShape = (
  mode: ToolMode,
  pointer: { x: number; y: number },
  canvas: Canvas
) => {
  const selectedColor: string = (canvas as any).annotationColor || "red";
  const config = getToolConfig(mode) as any;
  // apply selected color to config respecting tool semantics
  if (mode === "text") config.fill = selectedColor;
  if (mode === "rect" || mode === "circle" || mode === "line" || mode === "measure")
    config.stroke = selectedColor;
  if (mode === "arrow") {
    config.stroke = selectedColor;
  }

  const transformedPointer = transformPointer(pointer, canvas);
  const common = { id: uuidv4(), ...config };

  switch (mode) {
    case "text":
      return new Textbox("Text", {
        left: transformedPointer.x,
        top: transformedPointer.y,
        ...common,
      });
    case "rect":
    case "highlight":
      return new Rect({
        left: transformedPointer.x,
        top: transformedPointer.y,
        width: 1,
        height: 1,
        ...common,
      });
    case "line":
      return new Line(
        [transformedPointer.x, transformedPointer.y, transformedPointer.x, transformedPointer.y],
        { ...common }
      );
    case "circle":
      return new Circle({
        left: transformedPointer.x,
        top: transformedPointer.y,
        radius: 1,
        ...common,
      });
    case "crop":
      return new Rect({
        left: transformedPointer.x,
        top: transformedPointer.y,
        ...common,
      });
    case "measure": {
      return createMeasurementLine({ x: transformedPointer.x, y: transformedPointer.y }, common);
    }
    case "calibrate":
      return new Rect({
        left: transformedPointer.x,
        top: transformedPointer.y,
        width: 1,
        height: 1,
        ...common,
      });
    case "arrow": {
      return createArrow({ x: transformedPointer.x, y: transformedPointer.y }, common);
    }
  }
};

/*
Mutates 'shape' dimensions/geometry while dragging from startPoint to currentPoint.
Switches per mode; some tools (measure) get extra updates elsewhere.
*/
export const updateShapeSize = (
  shape: Rect | Circle | Line | FabricMeasurementLine | Textbox | Group,
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number },
  mode: ToolMode,
  canvas: Canvas
) => {
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  const width = Math.abs(currentPoint.x - startPoint.x);
  const height = Math.abs(currentPoint.y - startPoint.y);
  const left = Math.min(startPoint.x, currentPoint.x);
  const top = Math.min(startPoint.y, currentPoint.y);

  switch (mode) {
    case "text":
      break;
    case "rect":
    case "highlight":
    case "crop":
      shape.set({
        left: left,
        top: top,
        width: Math.max(1, width),
        height: Math.max(1, height),
      });
      break;
    case "line":
    case "calibrate":
      shape.set({
        x1: startPoint.x,
        y1: startPoint.y,
        x2: currentPoint.x,
        y2: currentPoint.y,
      });
      break;
    case "measure":
      updateMeasurementSize(shape as FabricMeasurementLine, startPoint, currentPoint);
      break;
    case "arrow": {
      updateArrowSize(shape as Group, startPoint, currentPoint);
      break;
    }

    case "circle": {
      const maxRadius = Math.min(
        (canvasWidth - left) / 2,
        (canvasHeight - top) / 2,
        width / 2,
        height / 2
      );
      const radius = Math.max(1, maxRadius);
      shape.set({
        left: left,
        top: top,
        radius: radius,
      });
      break;
    }
  }
};
