import { Canvas, FabricImage, Circle } from "fabric";

interface MagnifierState {
  lensImage?: FabricImage;
  maskCircle?: Circle;
  clipCircle?: Circle;
  originalImage?: FabricImage;
  scale: number;
  radius: number;
  eventCleanup?: () => void;
}

const canvasMagnifierStates = new Map<Canvas, MagnifierState>();
const magnifierStateSetters = new Map<Canvas, (value: boolean) => void>();

const defaultMagnifierConfig = {
  scale: 2,
  radius: 100,
};

/*
Gets or creates magnifier state for a canvas
*/
const getMagnifierState = (canvas: Canvas): MagnifierState => {
  let state = canvasMagnifierStates.get(canvas);
  if (!state) {
    state = {
      scale: defaultMagnifierConfig.scale,
      radius: defaultMagnifierConfig.radius,
    };
    canvasMagnifierStates.set(canvas, state);
  }
  return state;
};

/*
Creates magnified lens by creating new image from same source URL.
Follows existing pattern of creating fabric objects with proper configuration.
*/
const createMagnifierLens = async (canvas: Canvas): Promise<void> => {
  const state = getMagnifierState(canvas);

  if (!canvas.backgroundImage || state.lensImage) {
    return;
  }

  const originalImage = canvas.backgroundImage as FabricImage;
  if (!originalImage) {
    return;
  }

  const imageElement = originalImage.getElement() as HTMLImageElement;
  if (!imageElement || !imageElement.src) {
    return;
  }

  const { FabricImage } = await import("fabric");
  const lens = await FabricImage.fromURL(imageElement.src, {
    crossOrigin: "anonymous",
  });

  const originalScaleX = originalImage.scaleX || 1;
  const originalScaleY = originalImage.scaleY || 1;

  lens.set({
    left: 0,
    top: 0,
    scaleX: originalScaleX * state.scale,
    scaleY: originalScaleY * state.scale,
    selectable: false,
    evented: false,
    opacity: 1,
    visible: false,
    name: "magnifierLens",
    originX: "left",
    originY: "top",
  });

  const clipCircle = new Circle({
    radius: state.radius,
    left: 0,
    top: 0,
    originX: "center",
    originY: "center",
    fill: "black",
    selectable: false,
    evented: false,
    absolutePositioned: true,
  });

  lens.clipPath = clipCircle;

  const maskCircle = new Circle({
    radius: state.radius,
    left: 0,
    top: 0,
    originX: "center",
    originY: "center",
    fill: "transparent",
    strokeUniform: true,
    visible: false,
    selectable: false,
    evented: false,
    name: "magnifierBorder",
  });

  state.lensImage = lens;
  state.maskCircle = maskCircle;
  state.clipCircle = clipCircle;
  state.originalImage = originalImage;

  canvas.add(lens);
  canvas.add(maskCircle);

  canvas.bringObjectToFront(lens);
  canvas.bringObjectToFront(maskCircle);

  canvas.renderAll();
};

/*
Updates magnifier lens position based on mouse coordinates.
Positions lens to show magnified area under cursor.
*/
const updateMagnifierPosition = (canvas: Canvas, mouseX: number, mouseY: number): void => {
  const state = getMagnifierState(canvas);

  if (!state.lensImage || !state.maskCircle || !state.clipCircle) {
    return;
  }

  const lensLeft = -(state.scale - 1) * mouseX;
  const lensTop = -(state.scale - 1) * mouseY;

  state.lensImage.set({
    left: lensLeft,
    top: lensTop,
  });

  state.maskCircle.set({
    left: mouseX,
    top: mouseY,
  });

  state.clipCircle.set({
    left: mouseX,
    top: mouseY,
  });

  canvas.renderAll();
};

/*
Sets up mouse event handlers for magnifying glass.
Returns cleanup function to remove event listeners.
*/
const setupMagnifierEvents = (canvas: Canvas): (() => void) => {
  const state = getMagnifierState(canvas);

  const handleMouseMove = (e: { pointer?: { x: number; y: number } }) => {
    const pointer = e.pointer;
    if (!pointer) return;

    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    if (pointer.x >= 0 && pointer.x <= canvasWidth && pointer.y >= 0 && pointer.y <= canvasHeight) {
      updateMagnifierPosition(canvas, pointer.x, pointer.y);
    }
  };

  const handleMouseOut = () => {
    if (state.lensImage) {
      state.lensImage.set({ visible: false });
    }
    if (state.maskCircle) {
      state.maskCircle.set({ visible: false });
    }
    canvas.renderAll();
  };

  const handleMouseEnter = () => {
    if (state.lensImage) {
      state.lensImage.set({ visible: true });
    }
    if (state.maskCircle) {
      state.maskCircle.set({ visible: true });
    }
    canvas.renderAll();
  };

  canvas.on("mouse:move", handleMouseMove);
  canvas.on("mouse:out", handleMouseOut);
  canvas.on("mouse:over", handleMouseEnter);

  return () => {
    canvas.off("mouse:move", handleMouseMove);
    canvas.off("mouse:out", handleMouseOut);
    canvas.off("mouse:over", handleMouseEnter);
  };
};

/*
Activates magnifying glass functionality.
Creates lens and sets up event handlers.
*/
export const activateMagnifier = (canvas: Canvas): (() => void) => {
  const state = getMagnifierState(canvas);

  createMagnifierLens(canvas);
  const eventCleanup = setupMagnifierEvents(canvas);
  state.eventCleanup = eventCleanup;

  return () => {
    deactivateMagnifier(canvas);
  };
};

/*
Deactivates magnifying glass functionality.
Removes lens objects and cleans up event listeners.
*/
const deactivateMagnifier = (canvas: Canvas): void => {
  const state = getMagnifierState(canvas);

  if (state.lensImage) {
    canvas.remove(state.lensImage);
    state.lensImage = undefined;
  }

  if (state.maskCircle) {
    canvas.remove(state.maskCircle);
    state.maskCircle = undefined;
  }

  if (state.clipCircle) {
    state.clipCircle = undefined;
  }

  if (state.eventCleanup) {
    state.eventCleanup();
    state.eventCleanup = undefined;
  }

  canvas.renderAll();
};

/*
Checks if magnifier is active for a canvas by verifying objects exist and are on canvas
*/
const isMagnifierActive = (canvas: Canvas): boolean => {
  const state = canvasMagnifierStates.get(canvas);
  if (!state?.lensImage || !state?.maskCircle) return false;

  const canvasObjects = canvas.getObjects();
  return canvasObjects.includes(state.lensImage) && canvasObjects.includes(state.maskCircle);
};

/*
Creates magnifier handler function with reliable toggle logic.
Always activates/deactivates based on requested value, ensuring consistent behavior.
*/
export const createMagnifierHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setMagnifier: (value: boolean) => void
) => {
  return (value: boolean) => {
    const canvas = fabricCanvas.current;
    if (!canvas) return;

    magnifierStateSetters.set(canvas, setMagnifier);

    if (isMagnifierActive(canvas)) {
      deactivateMagnifier(canvas);
    }

    if (value) {
      activateMagnifier(canvas);
    }

    setMagnifier(value);
  };
};

/*
Deactivates magnifier when other tools are selected.
Called from tool management system. Updates both canvas and React state.
*/
export const deactivateMagnifierForTool = (canvas: Canvas): void => {
  if (isMagnifierActive(canvas)) {
    deactivateMagnifier(canvas);

    const stateSetter = magnifierStateSetters.get(canvas);
    if (stateSetter) {
      stateSetter(false);
    }
  }
};
