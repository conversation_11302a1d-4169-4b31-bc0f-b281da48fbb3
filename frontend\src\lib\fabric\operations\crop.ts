import { Canvas, Group, Point, Rect } from "fabric";
import { CropData, SavedAnnotationsSnapshot, TransformState } from "@/shared/types";
import { syncArrowFromSaved, isArrow } from "./arrows";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "./transforms";

/*
Computes container dimensions for restoring original image after crop undo.
Uses saved canvas dimensions or falls back to actual container rect.
*/
export const computeCropRestoreContainer = (
  cropData: CropData,
  fallbackRect?: DOMRect
): DOMRect | undefined => {
  if (!cropData.canvasDimensions) return fallbackRect;

  return {
    width: cropData.canvasDimensions.width,
    height: cropData.canvasDimensions.height,
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    bottom: cropData.canvasDimensions.height,
    right: cropData.canvasDimensions.width,
    toJSON: () => ({}),
  } as DOMRect;
};

/*
Applies crop transformation to canvas by cropping background image and adjusting annotations.
Creates new cropped background and repositions all annotations to match new canvas dimensions.
*/
export const applyCropTransformation = async (
  canvas: Canvas,
  cropData: CropData
): Promise<void> => {
  if (!cropData.normalizedCropRect) return;
  const cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
  if (cropRect) {
    canvas.remove(cropRect);
  }
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();
  const left = cropData.normalizedCropRect.left * canvasWidth;
  const top = cropData.normalizedCropRect.top * canvasHeight;
  const width = cropData.normalizedCropRect.width * canvasWidth;
  const height = cropData.normalizedCropRect.height * canvasHeight;

  const annotations = canvas
    .getObjects()
    .filter((obj) => (obj as any).name !== "backgroundImage" && (obj as any).name !== "cropRect");
  const previousVisibilities = annotations.map((obj) => obj.visible);
  annotations.forEach((obj) => obj.set({ visible: false }));
  canvas.renderAll();

  const fullCanvasImage = canvas.toDataURL();
  const croppedDataURL = await new Promise<string>((resolve) => {
    const img = new Image();
    img.onload = () => {
      const offscreenCanvas = document.createElement("canvas");
      offscreenCanvas.width = width;
      offscreenCanvas.height = height;
      const context2d = offscreenCanvas.getContext("2d")!;
      context2d.drawImage(img, left, top, width, height, 0, 0, width, height);
      resolve(offscreenCanvas.toDataURL());
    };
    img.src = fullCanvasImage;
  });

  annotations.forEach((obj, i) => obj.set({ visible: previousVisibilities[i] }));
  canvas.renderAll();

  const { FabricImage } = await import("fabric");
  const croppedImage = await FabricImage.fromURL(croppedDataURL, {
    crossOrigin: "anonymous",
  });

  canvas.setDimensions({ width, height });
  canvas.backgroundImage = croppedImage;
  croppedImage.set({
    left: width / 2,
    top: height / 2,
    originX: "center",
    originY: "center",
    selectable: false,
    evented: false,
    name: "backgroundImage",
  });

  canvas.forEachObject((obj) => {
    if ((obj as any).name === "backgroundImage") return;
    obj.set({
      left: (obj.left || 0) - left,
      top: (obj.top || 0) - top,
    });
  });
  canvas.renderAll();
};

/*
Creates handler function to toggle between crop apply and restore operations.
- Apply: saves normalized crop rect, rebuilds canvas, marks crop state
- Restore: restores original image and annotations, resets crop state
*/
export const createCropToggleHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: string,
  savedAnnotations?: SavedAnnotationsSnapshot,
  transformState?: TransformState,
  currentCropData?: CropData
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      await restoreOriginalCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        originalImageUrl || "",
        containerRef,
        savedAnnotations,
        transformState,
        currentCropData
      );
      return;
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "cropRect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    const normalizedCropRect = {
      left: left / canvasWidth,
      top: top / canvasHeight,
      width: width / canvasWidth,
      height: height / canvasHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
      transformStateAtCrop: transformState ? { ...transformState } : undefined,
    };

    await applyCropTransformation(canvas, cropResult);

    setCropData(cropResult);
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

/*
Sets common positional/transform/styling properties onto an annotation object
from saved normalized data.
*/
const setAnnotationProps = (obj: any, item: any, width: number, height: number) => {
  const base = {
    left: (item.leftNorm ?? 0) * width,
    top: (item.topNorm ?? 0) * height,
    angle: item.angle ?? 0,
    originX: item.preserveScale ? item.originX || "center" : "center",
    originY: item.preserveScale ? item.originY || "center" : "center",
    scaleX: item.preserveScale ? item.scaleX ?? 1 : 1,
    scaleY: item.preserveScale ? item.scaleY ?? 1 : 1,
    flipX: item.flipX ?? false,
    flipY: item.flipY ?? false,
  };
  const optionalKeys = ["stroke", "strokeWidth", "fill", "name", "id"] as const;
  const extras = Object.fromEntries(
    optionalKeys.filter((k) => item[k] !== undefined).map((k) => [k, item[k]])
  );
  obj.set({ ...base, ...extras });
};

/*
Mutates an existing Fabric object to match saved annotation data for its type.
Handles preserveScale paths and arrow groups specially.
*/
const syncAnnotation = (obj: any, item: any, width: number, height: number) => {
  if (obj.type !== item.type) return;

  switch (item.type) {
    case "line": {
      const x1Abs = (item.x1Norm ?? 0) * width;
      const y1Abs = (item.y1Norm ?? 0) * height;
      const x2Abs = (item.x2Norm ?? 0) * width;
      const y2Abs = (item.y2Norm ?? 0) * height;

      const left = (item.leftNorm ?? 0) * width;
      const top = (item.topNorm ?? 0) * height;

      obj.set({
        x1: x1Abs - left,
        y1: y1Abs - top,
        x2: x2Abs - left,
        y2: y2Abs - top,
      });
      setAnnotationProps(obj, item, width, height);
      obj.setCoords();
      break;
    }

    case "textbox": {
      obj.set({
        text: item.text || "",
        width: (item.widthNorm ?? 0) * width,
      });
      setAnnotationProps(obj, item, width, height);
      obj.setCoords();
      break;
    }

    case "text": {
      const isMeasurement = item.name === "measurementText";
      obj.set({
        text: item.text || (obj as any).text || "",
        ...(item.fontSize ? { fontSize: item.fontSize } : {}),
      });
      setAnnotationProps(obj, item, width, height);
      if (isMeasurement) {
        obj.set({ scaleX: 1, scaleY: 1, originX: "center", originY: "center" });
      }
      obj.setCoords();
      break;
    }

    case "rect": {
      // Prefer corner-based restore if present (rotation-proof)
      if (item.tlx != null && item.tly != null && item.brx != null && item.bry != null) {
        const x1 = item.tlx * width, y1 = item.tly * height;
        const x2 = item.brx * width, y2 = item.bry * height;

        const left = Math.min(x1, x2);
        const top = Math.min(y1, y2);
        const w = Math.abs(x2 - x1);
        const h = Math.abs(y2 - y1);

        obj.set({
          originX: "left",
          originY: "top",
          left, top, width: w, height: h,
          angle: 0, scaleX: 1, scaleY: 1,
        });
        if (item.stroke !== undefined) obj.set({ stroke: item.stroke });
        if (item.strokeWidth !== undefined) obj.set({ strokeWidth: item.strokeWidth });
        if (item.fill !== undefined) obj.set({ fill: item.fill });
        obj.setCoords();
        break;
      }

      obj.set({
        width: (item.widthNorm ?? 0) * width,
        height: (item.heightNorm ?? 0) * height,
      });
      setAnnotationProps(obj, item, width, height);
      obj.setCoords();
      break;
    }

    case "circle": {
      if (item.tlx != null && item.tly != null && item.brx != null && item.bry != null) {
        const x1 = item.tlx * width, y1 = item.tly * height;
        const x2 = item.brx * width, y2 = item.bry * height;

        const left = Math.min(x1, x2);
        const top = Math.min(y1, y2);
        const w = Math.abs(x2 - x1);
        const h = Math.abs(y2 - y1);
        const diam = Math.min(w, h);

        obj.set({
          originX: "left",
          originY: "top",
          left, top,
          radius: Math.max(0, diam / 2),
          angle: 0, scaleX: 1, scaleY: 1,
          strokeUniform: true,
        });
        if (item.stroke !== undefined) obj.set({ stroke: item.stroke });
        if (item.strokeWidth !== undefined) obj.set({ strokeWidth: item.strokeWidth });
        if (item.fill !== undefined) obj.set({ fill: item.fill });
        obj.setCoords();
        break;
      }

      const radius = Math.min((item.widthNorm ?? 0) * width, (item.heightNorm ?? 0) * height) / 2;
      obj.set({ radius: Math.max(0, radius) });
      setAnnotationProps(obj, item, width, height);
      obj.setCoords();
      break;
    }

    case "path": {
      if (!item.path) break;

      if (!item.preserveScale) {
        obj.set({ path: item.path });
        setAnnotationProps(obj, item, width, height);
      } else {
        const pathWidth = (item.widthNorm ?? 0) * width;
        const pathHeight = (item.heightNorm ?? 0) * height;

        const rebuilt = item.path.map((cmd: any[]) =>
          cmd.map((val: any, idx: number) =>
            typeof val === "number" ? (idx % 2 === 1 ? val * pathWidth : val * pathHeight) : val
          )
        );

        const optionalProps: Record<string, any> = {};
        ["stroke", "strokeWidth", "fill", "angle", "flipX", "flipY", "name", "id"].forEach(
          (k) => {
            if (item[k] !== undefined) optionalProps[k] = item[k];
          }
        );

        obj.set({
          path: rebuilt,
          originX: "center",
          originY: "center",
          left: (item.leftNorm ?? 0) * width,
          top: (item.topNorm ?? 0) * height,
          scaleX: 1,
          scaleY: 1,
          ...optionalProps,
        });

        (obj as any).pathOffset = new Point(pathWidth / 2, pathHeight / 2);
        (obj as any).dirty = true;
      }
      obj.setCoords();
      break;
    }
    case "group": {
      if (
        isArrow(obj) &&
        item.name === "arrow" &&
        item.x1Norm != null &&
        item.y1Norm != null &&
        item.x2Norm != null &&
        item.y2Norm != null
      ) {
        syncArrowFromSaved(obj as Group, item, width, height);
      } else {
        setAnnotationProps(obj, item, width, height);
      }
      obj.setCoords();
      break;
    }

    default:
      break;
  }
};

const getKey = (o: any) => o?.id ?? o?.name;
const hasSavedObjects = (s: any): s is { objects: any[] } => Array.isArray(s?.objects);

/*
Iterates saved annotations and syncs any matching existing objects by id/name.
*/
const syncAnnotations = async (canvas: Canvas, saved: SavedAnnotationsSnapshot) => {
  if (!hasSavedObjects(saved)) return;

  const width = canvas.getWidth();
  const height = canvas.getHeight();

  const existing = canvas.getObjects().filter((o) => {
    const name = (o as any).name;
    return name !== "backgroundImage" && name !== "cropRect";
  });

  const byKeyExisting = new Map<string, any>();
  for (const o of existing) {
    const k = getKey(o);
    if (k) byKeyExisting.set(k, o);
  }

  for (const item of saved.objects) {
    const key = getKey(item);
    if (!key) continue;
    const obj = byKeyExisting.get(key);
    if (!obj) continue;
    if (obj.type !== item.type) continue;

    syncAnnotation(obj, item, width, height);
    obj.setCoords();
  }

  canvas.requestRenderAll();
};

/*
Restores canvas to original uncropped state with proper transforms and annotations.
Rebuilds original image, reapplies transforms up to crop time plus deltas, restores annotations.
*/
export const restoreOriginalCanvas = async (
  canvas: Canvas,
  setCropData: (data: CropData) => void,
  setHasPerformedCrop: (value: boolean) => void,
  originalImageUrl: string,
  containerRef?: React.RefObject<HTMLElement | null>,
  savedAnnotations?: any,
  transformState?: TransformState,
  currentCropData?: CropData
) => {
  if (!canvas || !originalImageUrl) return;

  const containerRect = containerRef?.current?.getBoundingClientRect();
  if (!containerRect) return;

  const { FabricImage } = await import("fabric");
  const originalImage = await FabricImage.fromURL(originalImageUrl, {
    crossOrigin: "anonymous",
  });

  const imageAspect = (originalImage.width || 1) / (originalImage.height || 1);
  const containerAspect = containerRect.width / containerRect.height;

  let scale: number;
  let targetWidth: number;
  let targetHeight: number;

  if (imageAspect > containerAspect) {
    targetWidth = containerRect.width;
    targetHeight = containerRect.width / imageAspect;
    scale = targetWidth / (originalImage.width || 1);
  } else {
    targetHeight = containerRect.height;
    targetWidth = targetHeight * imageAspect;
    scale = targetHeight / (originalImage.height || 1);
  }

  canvas.setDimensions({ width: targetWidth, height: targetHeight });

  originalImage.set({
    left: targetWidth / 2,
    top: targetHeight / 2,
    originX: "center",
    originY: "center",
    scaleX: scale,
    scaleY: scale,
    selectable: false,
    evented: false,
    name: "backgroundImage",
  });
  canvas.backgroundImage = originalImage;

  const snapshotTransform = savedAnnotations?.transformStateAtSnapshot;
  const fallbackTransform = currentCropData?.transformStateAtCrop;
  const baseTransform = snapshotTransform || fallbackTransform;

  if (baseTransform) {
    if (baseTransform.rotations) {
      for (let i = 0; i < baseTransform.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (baseTransform.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas, baseTransform.rotations);
    }
    if (baseTransform.flipVertical) {
      applyCanvasFlipVertical(canvas, baseTransform.rotations);
    }
  }

  if (hasSavedObjects(savedAnnotations)) {
    await syncAnnotations(canvas, savedAnnotations);
  }

  if (transformState && baseTransform) {
    const fromRot = baseTransform.rotations || 0;
    const toRot = transformState.rotations || 0;
    const rotationDelta = (toRot - fromRot + 4) % 4;
    for (let i = 0; i < rotationDelta; i++) {
      applyCanvasRotation(canvas);
    }

    const flipXDelta = transformState.flipHorizontal !== baseTransform.flipHorizontal;
    const flipYDelta = transformState.flipVertical !== baseTransform.flipVertical;

    if (flipXDelta) {
      applyCanvasFlipHorizontal(canvas, transformState.rotations);
    }
    if (flipYDelta) {
      applyCanvasFlipVertical(canvas, transformState.rotations);
    }
  }

  setCropData({
    isCropped: false,
    normalizedCropRect: undefined,
    canvasDimensions: undefined,
  });
  setHasPerformedCrop(false);

  canvas.renderAll();
};

/*
Captures current annotations before cropping to preserve them for restoration.
*/
export const captureAnnotationsBeforeCrop = (
  canvas: Canvas,
  transformState?: TransformState
): SavedAnnotationsSnapshot | undefined => {
  const visibleObjs = canvas
    .getObjects()
    .filter((obj) => (obj as any).name !== "backgroundImage" && (obj as any).name !== "cropRect");

  if (!visibleObjs.length) return undefined;

  const width = canvas.getWidth();
  const height = canvas.getHeight();

  const objects = visibleObjs.map((obj) => {
    const center = obj.getCenterPoint();
    const base = {
      type: obj.type,
      name: (obj as any).name,
      angle: obj.angle || 0,
      fill: obj.fill,
      stroke: obj.stroke,
      strokeWidth: obj.strokeWidth,
      scaleX: obj.scaleX,
      scaleY: obj.scaleY,
      originX: (obj as any).originX,
      originY: (obj as any).originY,
      flipX: obj.flipX || false,
      flipY: obj.flipY || false,
      preserveScale: false,
      id: (obj as any).id,
      leftNorm: center.x / width,
      topNorm: center.y / height,
    } as any;

    if (obj.type === "line") {
      const line = obj as any;
      return {
        ...base,
        x1Norm: line.x1! / width,
        y1Norm: line.y1! / height,
        x2Norm: line.x2! / width,
        y2Norm: line.y2! / height,
      };
    }

    if (obj.type === "path") {
      return {
        ...base,
        path: (obj as any).path,
      };
    }

    return {
      ...base,
      widthNorm: obj.getScaledWidth() / width,
      heightNorm: obj.getScaledHeight() / height,
      ...((obj as any).text && { text: (obj as any).text }),
    };
  });

  const bg = canvas.backgroundImage as any;
  const bgTransformAtSnapshot = bg
    ? {
        angle: bg.angle || 0,
        flipX: !!bg.flipX,
        flipY: !!bg.flipY,
      }
    : undefined;

  return {
    objects,
    canvasWidth: width,
    canvasHeight: height,
    transformStateAtSnapshot: transformState ? { ...transformState } : undefined,
    bgTransformAtSnapshot,
  };
};

const mergeCroppedAnnotationsBasic = (
  canvas: Canvas,
  cropData: CropData,
  savedAnnotations: SavedAnnotationsSnapshot | undefined,
  transformState?: TransformState,
  hasPerformedCrop: boolean = true
): SavedAnnotationsSnapshot => {
  const originalCanvasWidth = cropData.canvasDimensions?.width || canvas.getWidth();
  const originalCanvasHeight = cropData.canvasDimensions?.height || canvas.getHeight();

  const { left = 0, top = 0, width = 1, height = 1 } = cropData.normalizedCropRect || {};
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();
  const scaleX = width * originalCanvasWidth;
  const scaleY = height * originalCanvasHeight;

  const currentObjs = canvas
    .getObjects()
    .filter((obj) => (obj as any).name !== "backgroundImage" && (obj as any).name !== "cropRect")
    .map((obj: any) => {
      const base: any = {
        type: obj.type,
        name: obj.name,
        angle: obj.angle || 0,
        fill: obj.fill,
        stroke: obj.stroke,
        strokeWidth: obj.strokeWidth,
        scaleX: obj.scaleX,
        scaleY: obj.scaleY,
        originX: obj.originX,
        originY: obj.originY,
        flipX: obj.flipX || false,
        flipY: obj.flipY || false,
        preserveScale: hasPerformedCrop,
        id: obj.id,
      };

      const fullLeft = left * originalCanvasWidth + (obj.left! / canvasWidth) * scaleX;
      const fullTop = top * originalCanvasHeight + (obj.top! / canvasHeight) * scaleY;
      base.leftNorm = fullLeft / originalCanvasWidth;
      base.topNorm = fullTop / originalCanvasHeight;

      if (obj.type === "line") {
        const getCoord = (val: number, axis: "x" | "y") =>
          (axis === "x" ? left * originalCanvasWidth : top * originalCanvasHeight) +
          (val / (axis === "x" ? canvasWidth : canvasHeight)) * (axis === "x" ? scaleX : scaleY);

        return {
          ...base,
          x1Norm: getCoord(obj.x1!, "x") / originalCanvasWidth,
          y1Norm: getCoord(obj.y1!, "y") / originalCanvasHeight,
          x2Norm: getCoord(obj.x2!, "x") / originalCanvasWidth,
          y2Norm: getCoord(obj.y2!, "y") / originalCanvasHeight,
        };
      }

      if (obj.type === "path") {
        const bounds = obj.getBoundingRect();
        const bw = bounds.width || 1;
        const bh = bounds.height || 1;
        const normPath = obj.path?.map((cmd: any) =>
          cmd.map((val: any, idx: number) => {
            if (typeof val !== "number") return val;
            const isX = idx % 2 === 1;
            return isX ? (val - bounds.left) / bw : (val - bounds.top) / bh;
          })
        );

        const center = obj.getCenterPoint();
        const leftFull =
          left * originalCanvasWidth + (center.x / canvasWidth) * (width * originalCanvasWidth);
        const topFull =
          top * originalCanvasHeight + (center.y / canvasHeight) * (height * originalCanvasHeight);

        return {
          type: "path",
          name: obj.name,
          id: obj.id,
          angle: obj.angle || 0,
          fill: obj.fill,
          stroke: obj.stroke,
          strokeWidth: obj.strokeWidth,
          originX: "center",
          originY: "center",
          flipX: obj.flipX || false,
          flipY: obj.flipY || false,
          preserveScale: hasPerformedCrop,
          leftNorm: leftFull / originalCanvasWidth,
          topNorm: topFull / originalCanvasHeight,
          widthNorm: (bw / canvasWidth) * width,
          heightNorm: (bh / canvasHeight) * height,
          path: normPath,
        };
      }

      const rawWidth = (obj.width ?? 0) * (obj.scaleX ?? 1);
      const rawHeight = (obj.height ?? 0) * (obj.scaleY ?? 1);

      return {
        ...base,
        widthNorm: (rawWidth / canvasWidth) * width,
        heightNorm: (rawHeight / canvasHeight) * height,
        ...(obj.text && { text: obj.text }),
      };
    });

  const existing = savedAnnotations?.objects || [];
  const existingIds = new Set(existing.map((obj: any) => obj.id));
  const filtered = currentObjs.filter((obj: any) => !existingIds.has(obj.id));

  const bg = canvas.backgroundImage as any;
  return {
    objects: [...existing, ...filtered],
    canvasWidth: originalCanvasWidth,
    canvasHeight: originalCanvasHeight,
    transformStateAtSnapshot: transformState ? { ...transformState } : undefined,
    bgTransformAtSnapshot: bg
      ? {
          angle: bg.angle || 0,
          flipX: !!bg.flipX,
          flipY: !!bg.flipY,
        }
      : savedAnnotations?.bgTransformAtSnapshot,
  };
};

const mergeCroppedAnnotationsWithTransform = (
  canvas: Canvas,
  cropData: CropData,
  savedAnnotations: SavedAnnotationsSnapshot | undefined,
  transformState?: TransformState,
  hasPerformedCrop: boolean = true
): SavedAnnotationsSnapshot => {
  const originalCanvasWidth = cropData.canvasDimensions?.width || canvas.getWidth();
  const originalCanvasHeight = cropData.canvasDimensions?.height || canvas.getHeight();

  const { left = 0, top = 0, width = 1, height = 1 } = cropData.normalizedCropRect || {};
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  const r = ((transformState?.rotations ?? 0) % 4 + 4) % 4;
  const flipH = !!transformState?.flipHorizontal;
  const flipV = !!transformState?.flipVertical;

  const invFlip = (u: number, v: number) => ({ u: flipH ? 1 - u : u, v: flipV ? 1 - v : v });
  const invRotate = (u: number, v: number) => {
    switch (r) {
      case 1: return { u: v, v: 1 - u };
      case 2: return { u: 1 - u, v: 1 - v };
      case 3: return { u: 1 - v, v: u };
      default: return { u, v };
    }
  };
  const fwdRotate = (x: number, y: number) => {
    switch (r) {
      case 1: return { x: 1 - y, y: x };
      case 2: return { x: 1 - x, y: 1 - y };
      case 3: return { x: y, y: 1 - x };
      default: return { x, y };
    }
  };
  const fwdFlip = (x: number, y: number) => ({ x: flipH ? 1 - x : x, y: flipV ? 1 - y : y });

  const mapCanvasToFullRotated = (xCanvas: number, yCanvas: number) => {
    const u = xCanvas / canvasWidth;
    const v = yCanvas / canvasHeight;
    const { u: uf, v: vf } = invFlip(u, v);
    const { u: u0, v: v0 } = invRotate(uf, vf);
    const X0 = left + u0 * width;
    const Y0 = top + v0 * height;
    const { x: xr, y: yr } = fwdRotate(X0, Y0);
    const { x, y } = fwdFlip(xr, yr);
    return { x, y };
  };

  const getObjCenter = (o: any) => o.getCenterPoint?.() ?? { x: (o.left ?? 0), y: (o.top ?? 0) };
  const getHalfExtents = (o: any) => ({ hw: o.getScaledWidth() / 2, hh: o.getScaledHeight() / 2 });

  const currentObjs = canvas
    .getObjects()
    .filter((o: any) => o?.name !== "backgroundImage" && o?.name !== "cropRect")
    .map((obj: any) => {
      const base: any = {
        type: obj.type,
        name: obj.name,
        angle: obj.angle || 0,
        fill: obj.fill,
        stroke: obj.stroke,
        strokeWidth: obj.strokeWidth,
        scaleX: obj.scaleX,
        scaleY: obj.scaleY,
        originX: obj.originX,
        originY: obj.originY,
        flipX: obj.flipX || false,
        flipY: obj.flipY || false,
        preserveScale: hasPerformedCrop,
        id: obj.id,
      };

      const anchor = mapCanvasToFullRotated(obj.left ?? 0, obj.top ?? 0);
      base.leftNorm = anchor.x;
      base.topNorm = anchor.y;

      if (obj.type === "line") {
        const x1 = (obj.left ?? 0) + (obj.x1 ?? 0);
        const y1 = (obj.top ?? 0) + (obj.y1 ?? 0);
        const x2 = (obj.left ?? 0) + (obj.x2 ?? 0);
        const y2 = (obj.top ?? 0) + (obj.y2 ?? 0);
        const p1 = mapCanvasToFullRotated(x1, y1);
        const p2 = mapCanvasToFullRotated(x2, y2);
        return { ...base, x1Norm: p1.x, y1Norm: p1.y, x2Norm: p2.x, y2Norm: p2.y };
      }

      if (obj.type === "rect") {
        const c = getObjCenter(obj);
        const { hw, hh } = getHalfExtents(obj);
        const tl = mapCanvasToFullRotated(c.x - hw, c.y - hh);
        const br = mapCanvasToFullRotated(c.x + hw, c.y + hh);
        return {
          type: "rect",
          name: obj.name,
          id: obj.id,
          preserveScale: hasPerformedCrop,
          tlx: tl.x, tly: tl.y,
          brx: br.x, bry: br.y,
          stroke: obj.stroke,
          strokeWidth: obj.strokeWidth,
          fill: obj.fill,
        };
      }

      if (obj.type === "circle") {
        const c = getObjCenter(obj);
        const diam = Math.min(obj.getScaledWidth(), obj.getScaledHeight());
        const r = diam / 2;
        const tl = mapCanvasToFullRotated(c.x - r, c.y - r);
        const br = mapCanvasToFullRotated(c.x + r, c.y + r);
        return {
          type: "circle",
          name: obj.name,
          id: obj.id,
          preserveScale: hasPerformedCrop,
          tlx: tl.x, tly: tl.y,
          brx: br.x, bry: br.y,
          stroke: obj.stroke,
          strokeWidth: obj.strokeWidth,
          fill: obj.fill,
        };
      }

      if (obj.type === "path") {
        const path = obj as any;

        const c = path.getCenterPoint();
        const cFull = mapCanvasToFullRotated(c.x, c.y);

        const bounds = path.getBoundingRect();
        const bw = Math.max(1, bounds.width || 0);
        const bh = Math.max(1, bounds.height || 0);

        const normPath = path.path?.map((cmd: any[]) =>
          cmd.map((val: any, idx: number) => {
            if (typeof val !== "number") return val;
              const isX = idx % 2 === 1;
              return isX ? (val - bounds.left) / bw : (val - bounds.top) / bh;
            })
        );

        return {
          type: "path",
          name: path.name,
          id: path.id,
          angle: path.angle || 0,
          fill: path.fill,
          stroke: path.stroke,
          strokeWidth: path.strokeWidth,
          originX: "center",
          originY: "center",
          flipX: !!path.flipX,
          flipY: !!path.flipY,
          preserveScale: hasPerformedCrop,
          leftNorm: cFull.x,
          topNorm:  cFull.y,
          widthNorm:  (bw / canvasWidth)  * width,
          heightNorm: (bh / canvasHeight) * height,
          path: normPath,
        };
      }


      if (obj.type === "group" && (obj as any).name === "arrow") {
        const g = obj as Group;
        const kids = (g as any)._objects as any[] | undefined;
        const shaft = kids?.find((k) => k.type === "line");
        const p1 = mapCanvasToFullRotated(g.left ?? 0, g.top ?? 0);
        const p2 = mapCanvasToFullRotated(
          (g.left ?? 0) + (shaft?.x2 ?? 0),
          (g.top ?? 0) + (shaft?.y2 ?? 0)
        );
        return {
          type: "group",
          name: (g as any).name,
          id: (g as any).id,
          preserveScale: hasPerformedCrop,
          originX: "center",
          originY: "center",
          x1Norm: p1.x, y1Norm: p1.y,
          x2Norm: p2.x, y2Norm: p2.y,
        };
      }

      if (obj.type === "text" && obj.name === "measurementText") {
        const c = obj.getCenterPoint();
        const cFull = mapCanvasToFullRotated(c.x, c.y);
        return {
          type: "text",
          name: obj.name,
          id: obj.id,
          preserveScale: hasPerformedCrop,
          originX: "center",
          originY: "center",
          leftNorm: cFull.x,
          topNorm: cFull.y,
          text: obj.text,
          fontSize: obj.fontSize,
          stroke: obj.stroke,
          strokeWidth: obj.strokeWidth,
          fill: obj.fill,
        };
      }

      return {
        ...base,
        widthNorm: (obj.getScaledWidth() / canvasWidth) * width,
        heightNorm: (obj.getScaledHeight() / canvasHeight) * height,
        ...(obj.text ? { text: obj.text } : {}),
      };
    });

  const existing = savedAnnotations?.objects || [];
  const existingIds = new Set(existing.map((o: any) => o.id));
  const filtered = currentObjs.filter((o: any) => !existingIds.has(o.id));

  const bg = canvas.backgroundImage as any;
  return {
    objects: [...existing, ...filtered],
    canvasWidth: originalCanvasWidth,
    canvasHeight: originalCanvasHeight,
    transformStateAtSnapshot: transformState ? { ...transformState } : undefined,
    bgTransformAtSnapshot: bg
      ? { angle: bg.angle || 0, flipX: !!bg.flipX, flipY: !!bg.flipY }
      : savedAnnotations?.bgTransformAtSnapshot,
  };
};

/*
Merges annotations created on cropped canvas with previously saved annotations.
*/
export const mergeAnnotationsFromCroppedCanvas = (
  canvas: Canvas,
  cropData: CropData,
  savedAnnotations: SavedAnnotationsSnapshot | undefined,
  transformState?: TransformState,
  hasPerformedCrop: boolean = true
): SavedAnnotationsSnapshot => {
  const atCropRot = (cropData.transformStateAtCrop?.rotations ?? 0) % 4;
  const curRot = (transformState?.rotations ?? 0) % 4;
  const rotatedCrop = atCropRot === 0 && (curRot === 1 || curRot === 2 || curRot === 3);

  if (rotatedCrop) {
    return mergeCroppedAnnotationsWithTransform(canvas, cropData, savedAnnotations, transformState, hasPerformedCrop);
  }
  return mergeCroppedAnnotationsBasic(canvas, cropData, savedAnnotations, transformState, hasPerformedCrop);
};
