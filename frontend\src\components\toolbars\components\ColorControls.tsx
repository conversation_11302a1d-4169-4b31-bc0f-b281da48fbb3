import React from "react";
import { FaPalette } from "react-icons/fa";
import { Canvas } from "fabric";
import { changeSelectedAnnotationColors } from "@/lib/fabric/operations/colorChange";

type ColorControlsProps = {
  color: string;
  onChange: (color: string) => void;
  fabricCanvas?: React.RefObject<Canvas | null>;
};

const ColorControls: React.FC<ColorControlsProps> = ({ color, onChange, fabricCanvas }) => {
  const handleColorChange = (newColor: string) => {
    onChange(newColor);
    const canvas = fabricCanvas?.current;
    if (canvas) {
      changeSelectedAnnotationColors(canvas, newColor);
    }
  };

  return (
    <div className="tool-grid">
      <label
        className="tool-btn color-picker-btn"
        title="Annotation Color"
        style={{ backgroundColor: color }}
      >
        <FaPalette />
        <input
          type="color"
          value={color}
          onChange={(e) => handleColorChange(e.target.value)}
          aria-label="annotation-color-picker"
          className="color-input-hidden"
        />
      </label>
    </div>
  );
};

export default ColorControls;
