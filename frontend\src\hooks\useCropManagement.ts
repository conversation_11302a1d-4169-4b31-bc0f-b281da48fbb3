import { useState } from "react";
import { Canvas } from "fabric";
import { CropData, CropManagementState } from "@/shared/types";

/*
Simple crop management using CSS clip-path.
Crops by applying clip-path to canvas container and scaling to fill available space.
*/
export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData,
  containerRef?: React.RefObject<HTMLElement | null>
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);

  const handleCrop = () => {
    if (!fabricCanvas?.current || !containerRef?.current) return;

    const canvas = fabricCanvas.current;
    
    if (cropData.isCropped) {
      // Remove crop - reset clip-path and scaling
      const container = containerRef.current;
      container.style.clipPath = '';
      container.style.transform = '';
      container.style.transformOrigin = '';
      
      setCropData({
        isCropped: false,
        normalizedCropRect: undefined,
      });
      return;
    }

    // Find crop rectangle
    let cropRect = canvas.getActiveObject();
    if (!(cropRect as any)?.name || (cropRect as any).name !== "cropRect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
    }

    if (!cropRect) return;

    // Get crop rectangle bounds
    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    // Calculate normalized crop rectangle
    const normalizedCropRect = {
      left: left / canvasWidth,
      top: top / canvasHeight,
      width: width / canvasWidth,
      height: height / canvasHeight,
    };

    // Apply clip-path to container
    const container = containerRef.current;
    const clipPath = `inset(${normalizedCropRect.top * 100}% ${(1 - normalizedCropRect.left - normalizedCropRect.width) * 100}% ${(1 - normalizedCropRect.top - normalizedCropRect.height) * 100}% ${normalizedCropRect.left * 100}%)`;
    
    container.style.clipPath = clipPath;
    
    // Scale to fill available space
    const scaleX = 1 / normalizedCropRect.width;
    const scaleY = 1 / normalizedCropRect.height;
    const scale = Math.min(scaleX, scaleY); // Use smaller scale to maintain aspect ratio
    
    container.style.transform = `scale(${scale})`;
    container.style.transformOrigin = `${normalizedCropRect.left * 100 + normalizedCropRect.width * 50}% ${normalizedCropRect.top * 100 + normalizedCropRect.height * 50}%`;

    // Remove crop rectangle from canvas
    canvas.remove(cropRect);
    canvas.renderAll();

    setCropData({
      isCropped: true,
      normalizedCropRect,
    });
  };

  return {
    cropData,
    setCropData,
    handleCrop,
  };
};
