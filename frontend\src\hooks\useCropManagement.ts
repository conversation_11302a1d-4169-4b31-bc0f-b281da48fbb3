import { useState } from "react";
import { Canvas } from "fabric";
import {
  CropData,
  CropManagementState,
  UndoTrackingState,
  TransformState,
  SavedAnnotationsSnapshot,
} from "@/shared/types";
import {
  createCropToggleHandler,
  captureAnnotationsBeforeCrop,
  mergeAnnotationsFromCroppedCanvas,
} from "@/lib/fabric/operations";

/*
Manages crop state and coordinates annotation persistence during crop operations.
Delegates complex annotation normalization to operations layer.
*/
export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData,
  undoTracking: UndoTrackingState,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: string,
  transformState?: TransformState
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);
  const [hasPerformedCrop, setHasPerformedCrop] = useState(initialCropData.isCropped || false);
  const [savedAnnotations, setSavedAnnotations] = useState<SavedAnnotationsSnapshot | undefined>(
    undefined
  );

  // Handles crop toggle: saves annotations before cropping, restores after uncropping
  const handleCrop = async () => {
    // Save annotations before first crop
    if (!hasPerformedCrop && fabricCanvas.current) {
      const snapshot = captureAnnotationsBeforeCrop(fabricCanvas.current, transformState);
      if (snapshot) {
        setSavedAnnotations(snapshot);
      }
    }

    const cropOperation = createCropToggleHandler(
      fabricCanvas,
      hasPerformedCrop,
      setCropData,
      undoTracking.isUndoingRef,
      setHasPerformedCrop,
      containerRef,
      originalImageUrl,
      savedAnnotations,
      transformState,
      cropData
    );

    await cropOperation();

    if (hasPerformedCrop) setSavedAnnotations(undefined);
  };

  // Merges new annotations created while cropped with existing saved annotations
  const handleShapeCreated = () => {
    if (!fabricCanvas.current || !hasPerformedCrop) return;

    const merged = mergeAnnotationsFromCroppedCanvas(
      fabricCanvas.current,
      cropData,
      savedAnnotations,
      transformState,
      hasPerformedCrop
    );

    setSavedAnnotations(merged);
  };

  return {
    cropData,
    setCropData,
    hasPerformedCrop,
    setHasPerformedCrop,
    handleCrop,
    handleShapeCreated,
  };
};
