import { Canvas } from "fabric";
import { deactivateMagnifierForTool, activateMagnifier } from "@/lib/fabric/operations/magnifier";

/*
Sets canvas interaction mode and per-object selectability/controls based on toolMode.
- 'freehand': enables brush drawing
- 'select': enables selection and controls for non-background objects
*/
export const setToolMode = (toolMode: string, canvas: Canvas) => {
  if (toolMode !== "magnifier") {
    deactivateMagnifierForTool(canvas);
  }

  canvas.selection = false;
  canvas.discardActiveObject();
  canvas.forEachObject((obj) => {
    const objectName = (obj as any as Record<string, any>)?.name;
    if (objectName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });
  canvas.defaultCursor = "crosshair";
  canvas.isDrawingMode = false;

  if (toolMode === "magnifier") {
    canvas.defaultCursor = "crosshair";
    canvas.isDrawingMode = false;
    canvas.selection = false;
    activateMagnifier(canvas);
  } else if (toolMode === "freehand") {
    canvas.isDrawingMode = true;
  } else if (toolMode === "select") {
    canvas.defaultCursor = "default";
    canvas.isDrawingMode = false;
    canvas.selection = true;

    canvas.forEachObject((obj) => {
      const objectName = (obj as any as Record<string, any>)?.name;

      if (
        objectName !== "backgroundImage" &&
        objectName !== "measurementText" &&
        objectName !== "arrowHead"
      ) {
        obj.selectable = true;
        obj.evented = true;
        obj.hasControls = true;
        obj.hasBorders = true;
      } else {
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
      }

      obj.setCoords();
    });
  }
  canvas.renderAll();
};
